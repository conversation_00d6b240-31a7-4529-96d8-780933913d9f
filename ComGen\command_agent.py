# command-generator-service/command_agent.py
import logging
import json
import os
import re
from typing import Dict, List

try:
    import google.generativeai as genai
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    genai = None

logger = logging.getLogger(__name__)

class CommandAgent:
    """
    Uses a generative AI model to determine the setup and run commands
    for a software project based on its key files.
    """
    def __init__(self, file_data: List[Dict]):
        self.file_data = file_data
        self.gemini_model = None

        if not genai:
            logger.error("'google-generativeai' or 'python-dotenv' not installed.")
            return

        api_key = os.getenv('GEMINI_API_KEY')
        if api_key:
            try:
                genai.configure(api_key=api_key)
                self.gemini_model = genai.GenerativeModel('gemini-1.5-flash-latest')
                logger.info("CommandAgent initialized successfully with Gemini model.")
            except Exception as e:
                logger.error(f"Failed to initialize Gemini model: {e}")
        else:
            logger.error("GEMINI_API_KEY not found. CommandAgent cannot function.")

    def generate_commands(self) -> Dict:
        """
        Builds a prompt, calls the Gemini API, and parses the response.
        """
        if not self.gemini_model:
            return {"error": "AI model is not available."}

        prompt = self._build_prompt()
        logger.info("Sending request to Gemini to generate commands...")
        
        try:
            response = self.gemini_model.generate_content(prompt)
            logger.debug(f"Raw Gemini response:\n{response.text}")
            return self._parse_response(response.text)
        except Exception as e:
            logger.error(f"Error during Gemini API call: {e}", exc_info=True)
            return {"error": f"An error occurred during AI analysis: {e}"}

    def _build_prompt(self) -> str:
        """Constructs a detailed prompt for the AI model."""
        prompt = """
You are an expert developer assistant. Your task is to analyze the provided files from a software project and generate the shell commands needed to install its dependencies and run it.

Here are the relevant files and their content:
"""
        for file_info in self.file_data:
            path = file_info.get("path")
            content = file_info.get("content", "Content not provided.")
            truncated_content = (content[:1500] + "\n...") if len(content) > 1500 else content
            prompt += f"\n--- File: {path} ---\n{truncated_content}\n"
        
        prompt += """
Based on this information, provide the commands to run this project.
Return ONLY a single, valid JSON object with two keys:
1. "setup_commands": A list of strings for installing dependencies (e.g., ["npm install"]).
2. "run_commands": A list of strings for starting the main application (e.g., ["npm start"]).

For a multi-service project (e.g., using Docker Compose), provide the single command to start everything.
Example for a Node.js project:
{
  "setup_commands": ["npm install"],
  "run_commands": ["npm start"]
}
Example for a Dockerized project:
{
  "setup_commands": ["docker-compose build"],
  "run_commands": ["docker-compose up"]
}

Do not include any other text, explanations, or markdown formatting.
"""
        logger.debug(f"Generated Gemini Prompt:\n{prompt}")
        return prompt

    def _parse_response(self, response_text: str) -> Dict:
        """Robustly parses the JSON object from the AI's response text."""
        try:
            # Use regex to find a JSON object within the text, ignoring surrounding text
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                data = json.loads(json_str)

                # Basic validation of the structure
                if "setup_commands" in data and "run_commands" in data:
                    logger.info("Successfully parsed command structure from Gemini response.")
                    return data
            
            logger.warning("Could not parse a valid command structure from AI response.")
            return {"error": "AI response was malformed.", "raw_response": response_text}
        except json.JSONDecodeError as e:
            logger.error(f"JSON decoding failed for AI response: {e}")
            return {"error": "Failed to decode AI response as JSON.", "raw_response": response_text}