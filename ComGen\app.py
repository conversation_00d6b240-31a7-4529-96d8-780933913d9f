# command-generator-service/app.py
import os
import logging
import uuid
import sys
from flask import Flask, request, jsonify, g, has_request_context
from command_agent import CommandAgent

# --- Logging Setup ---
logger = logging.getLogger("CommandGeneratorService")
logger.setLevel(logging.INFO)

class RequestIdFilter(logging.Filter):
    def filter(self, record):
        if has_request_context():
            record.request_id = g.get('request_id', 'N/A')
        else:
            record.request_id = 'startup'
        return True

if not logger.handlers:
    handler = logging.StreamHandler(sys.stdout)
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - [RequestID: %(request_id)s] - %(message)s')
    handler.setFormatter(formatter)
    logger.addFilter(RequestIdFilter())
    logger.addHandler(handler)
# --- End Logging ---

app = Flask(__name__)

@app.before_request
def start_request_logging():
    g.request_id = str(uuid.uuid4().hex[:12])
    logger.info(f"Incoming request: {request.method} {request.path}")

@app.route('/generate-commands', methods=['POST'])
def generate_run_commands():
    """
    Receives a list of critical files with their content and uses AI
    to generate setup and run commands.
    """
    logging.info("Received request to generate run commands.")
    if not request.is_json:
        return jsonify({"error": "Request must be JSON"}), 400

    data = request.get_json()
    # The expected payload is the direct output from the Triage service,
    # but now with 'content' added to each file dictionary.
    files_with_content = data.get('files_to_read')

    if not files_with_content or not isinstance(files_with_content, list):
        return jsonify({"error": "'files_to_read' key must contain a list"}), 400

    try:
        agent = CommandAgent(files_with_content)
        if not agent.gemini_model:
             return jsonify({"error": "AI Agent is not configured. Check API key."}), 503

        result = agent.generate_commands()
        return jsonify(result)

    except Exception as e:
        logger.error(f"Unexpected error in command generation: {e}", exc_info=True)
        return jsonify({"error": "Internal server error"}), 500

@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({"service": "CommandGeneratorService", "status": "healthy"})

if __name__ == '__main__':
    # Use a new port to avoid conflicts
    port = int(os.environ.get('PORT', 8002))
    logger.info(f"CommandGeneratorService starting on http://0.0.0.0:{port}")
    app.run(host='0.0.0.0', port=port, debug=True)