# GitCrawl

A Docker-based service for crawling GitHub repositories and returning their complete directory tree structure in JSON format.

## Features

- 🐳 **Docker-based isolation**: Each repository is crawled in a separate container for security and isolation
- 🌳 **Complete directory tree**: Returns full repository structure with file metadata
- 🔒 **Security-focused**: Input validation, rate limiting, and safe file handling
- 🚀 **Fast and efficient**: Shallow cloning and optimized tree generation
- 📊 **Detailed metadata**: File sizes, types, and repository information
- 🔄 **Fallback support**: Direct crawling when Docker is not available

## Quick Start

### Using Docker Compose (Recommended)

1. Clone this repository:
```bash
git clone <your-repo-url>
cd GitCrawl
```

2. Start the service:
```bash
docker-compose up -d
```

3. Test the service:
```bash
curl "http://localhost:8000/crawl/simple?url=https://github.com/octocat/Hello-World"
```

### Manual Docker Build

```bash
# Build the image
docker build -t gitcrawl .

# Run the container
docker run -d -p 8000:8000 -v /var/run/docker.sock:/var/run/docker.sock gitcrawl
```

### Local Development

```bash
# Install dependencies
pip install -r requirements.txt

# Run the application
python app.py
```

## API Documentation

### Health Check
```
GET /
```

Returns service status and configuration.

**Response:**
```json
{
  "service": "GitCrawl",
  "status": "healthy",
  "version": "1.0.0",
  "docker_available": true
}
```

### Crawl Repository (POST)
```
POST /crawl
Content-Type: application/json
```

**Request Body:**
```json
{
  "url": "https://github.com/owner/repository",
  "include_hidden": false,
  "use_docker": true
}
```

**Parameters:**
- `url` (required): GitHub repository URL
- `include_hidden` (optional): Include hidden files and directories (default: false)
- `use_docker` (optional): Use Docker container for crawling (default: true)

### Crawl Repository (GET)
```
GET /crawl/simple?url=<github-url>&include_hidden=<boolean>&use_docker=<boolean>
```

**Query Parameters:**
- `url` (required): GitHub repository URL
- `include_hidden` (optional): Include hidden files (default: false)
- `use_docker` (optional): Use Docker container (default: true)

### API Documentation
```
GET /api/docs
```

Returns complete API documentation.

## Response Format

```json
{
  "repository": {
    "url": "https://github.com/octocat/Hello-World",
    "owner": "octocat",
    "name": "Hello-World",
    "full_name": "octocat/Hello-World"
  },
  "tree": {
    "name": "Hello-World",
    "type": "directory",
    "path": "",
    "children": [
      {
        "name": "README",
        "type": "file",
        "path": "README",
        "size": 1234,
        "modified": 1640995200.0
      },
      {
        "name": "src",
        "type": "directory",
        "path": "src",
        "children": [
          {
            "name": "main.py",
            "type": "file",
            "path": "src/main.py",
            "size": 567,
            "modified": 1640995200.0
          }
        ]
      }
    ]
  },
  "metadata": {
    "total_files": 2,
    "total_directories": 1
  }
}
```

## Usage Examples

### Basic Usage
```bash

curl -i -X POST http://localhost:8000/crawl -H "Content-Type: application/json" -d '{"url": "https://github.com/AliSoua/Mailer-Service"}'

# Using GET endpoint
curl "http://localhost:8000/crawl/simple?url=https://github.com/octocat/Hello-World"
```

### Include Hidden Files
```bash
curl -X POST http://localhost:8000/crawl \
  -H "Content-Type: application/json" \
  -d '{"url": "https://github.com/octocat/Hello-World", "include_hidden": true}'
```

### Force Direct Crawling (No Docker)
```bash
curl -X POST http://localhost:8000/crawl \
  -H "Content-Type: application/json" \
  -d '{"url": "https://github.com/octocat/Hello-World", "use_docker": false}'
```

### Python Client Example
```python
import requests

# Crawl repository
response = requests.post('http://localhost:8000/crawl', json={
    'url': 'https://github.com/octocat/Hello-World',
    'include_hidden': False
})

if response.status_code == 200:
    tree_data = response.json()
    print(f"Repository: {tree_data['repository']['full_name']}")
    print(f"Total files: {tree_data['metadata']['total_files']}")
else:
    print(f"Error: {response.json()}")
```

## Configuration

### Environment Variables

- `PORT`: Server port (default: 8000)
- `FLASK_ENV`: Flask environment (development/production)
- `PYTHONUNBUFFERED`: Python output buffering (set to 1)

### Docker Configuration

The service requires access to the Docker daemon to create isolated containers for crawling. Mount the Docker socket:

```bash
-v /var/run/docker.sock:/var/run/docker.sock
```

## Security Features

- **Input validation**: GitHub URL format validation
- **Rate limiting**: Prevents abuse (configurable)
- **Container isolation**: Each crawl runs in a separate container
- **Path traversal protection**: Prevents directory traversal attacks
- **File type filtering**: Only processes safe file types
- **Size limits**: Prevents processing of extremely large repositories

## Error Handling

The API returns structured error responses:

```json
{
  "error": "Error message",
  "type": "error_type",
  "details": {
    "additional": "information"
  }
}
```

**Error Types:**
- `validation_error`: Invalid input data
- `repository_error`: Repository access issues
- `docker_error`: Docker-related problems
- `network_error`: Network connectivity issues
- `internal_error`: Unexpected server errors

## Limitations

- Only supports public GitHub repositories
- Repository size should be reasonable (< 500MB recommended)
- Rate limited to prevent abuse
- Requires Docker for full functionality

## Development

### Project Structure
```
GitCrawl/
├── app.py                 # Main Flask application
├── repo_crawler.py        # Repository crawling logic
├── docker_manager.py      # Docker container management
├── validators.py          # Input validation utilities
├── error_handlers.py      # Error handling and custom exceptions
├── requirements.txt       # Python dependencies
├── Dockerfile            # Docker image configuration
├── docker-compose.yml    # Docker Compose configuration
└── README.md            # This file
```

### Running Tests

```bash
# Install test dependencies
pip install pytest requests

# Run tests (when implemented)
pytest tests/
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.






