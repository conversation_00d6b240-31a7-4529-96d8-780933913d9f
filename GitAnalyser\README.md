# AI-Enhanced Stack Detection Agent

This is an intelligent technology stack detection service that combines rule-based analysis with Google's Gemini AI model for enhanced accuracy and discovery of complex technology patterns.

## Features

- **Rule-Based Detection**: Fast detection of common technologies through file patterns and dependencies
- **AI-Enhanced Analysis**: Uses Google Gemini to analyze code structure and content for deeper insights
- **Hybrid Approach**: Combines both methods for comprehensive stack detection
- **Flexible API**: Supports both simple file structure and rich content analysis

## Setup

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Configure Gemini API (Optional)

1. Get your Gemini API key from [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Copy `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```
3. Add your API key to `.env`:
   ```
   GEMINI_API_KEY=your_actual_api_key_here
   ```

### 3. Run the Service

```bash
python app.py
```

The service will start on `http://localhost:8001`

## API Usage

### Analyze Endpoint

**POST** `/analyze`

#### Request Format

```json
{
  "tree": [
    { "name": "package.json", "type": "file" },
    { "name": "src", "type": "directory", "children": [...] }
  ],
  "file_contents": {
    "package.json": "{\"dependencies\": {\"react\": \"^18.0.0\"}}"
  }
}
```

#### Response Format

```json
{
  "detected_stack": ["React", "NPM", "TypeScript"],
  "analysis_method": "AI-Enhanced",
  "gemini_enabled": true
}
```

#### Query Parameters

- `use_gemini=false`: Disable AI analysis and use only rule-based detection

### Health Check

**GET** `/health`

## Detection Strategies

### 1. Rule-Based Detection
- File presence (e.g., `package.json` → NPM)
- Path wildcards (e.g., `.github/workflows/*.yml` → GitHub Actions)
- Dependency analysis (parsing `package.json`, `requirements.txt`, etc.)
- Content matching (searching for specific strings in files)

### 2. AI-Enhanced Detection (Gemini)
- Analyzes file structure patterns
- Understands code content and context
- Identifies complex technology relationships
- Discovers technologies not covered by rules

## Examples

### Test with AI Enhancement
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  --data "@ai_test.json" \
  "http://localhost:8001/identify-files"
```

## Supported Technologies

The agent can detect a wide range of technologies including:

- **Languages**: JavaScript, TypeScript, Python, Java, C#, Go, PHP, Ruby
- **Frameworks**: React, Angular, Vue.js, Next.js, Django, Flask, Spring Boot
- **Package Managers**: NPM, Maven, Gradle, Pip, Composer, Cargo
- **DevOps**: Docker, GitHub Actions, Terraform
- **Databases**: PostgreSQL, MongoDB, Redis (via dependencies)
- **And many more through AI analysis**

## Configuration

### Environment Variables

- `GEMINI_API_KEY`: Your Google Gemini API key
- `USE_GEMINI`: Set to `false` to disable AI analysis globally
- `PORT`: Service port (default: 8001)

## Error Handling

The service gracefully handles:
- Missing Gemini API key (falls back to rule-based detection)
- Invalid JSON payloads
- Network errors during AI analysis
- Malformed file contents

## Performance

- **Rule-based detection**: ~10-50ms
- **AI-enhanced detection**: ~1-3 seconds (depending on content size)
- **Hybrid approach**: Combines speed of rules with intelligence of AI
