# 1. Start from a slim Python base image
FROM python:3.11-slim

# 2. Install git using the system package manager
#    - apt-get update: Refreshes package lists
#    - apt-get install -y git: Installs git without prompting
#    - rm -rf /var/lib/apt/lists/*: Cleans up to keep the image small
RUN apt-get update && \
    apt-get install -y git && \
    rm -rf /var/lib/apt/lists/*

# 3. Set the working directory inside the container
WORKDIR /app

# 4. Copy the requirements file into the container
COPY requirements_container.txt .

# 5. Install the required Python packages
RUN pip install --no-cache-dir -r requirements_container.txt

# 6. Copy the crawler script into the container
COPY container_crawler.py .

# 7. Set the default command to run when the container starts
#    This will execute our script. Arguments will be appended by the DockerManager.
ENTRYPOINT ["python", "container_crawler.py"]