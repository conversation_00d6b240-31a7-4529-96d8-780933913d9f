# orchestration_service.py

import uuid
import logging
import json
from typing import Dict, Any, Optional

from docker_manager import DockerManager

logger = logging.getLogger(__name__)

class OrchestrationService:
    """Orchestrates crawl jobs and their container lifecycles."""

    IMAGE_NAME = "git-crawler-image"
    
    def __init__(self, docker_manager: DockerManager):
        self.docker_manager = docker_manager
        # In-memory job store. For production, use Redis or a database for persistence.
        self.jobs: Dict[str, Dict[str, Any]] = {}

    def start_crawl_job(self, github_url: str, include_hidden: bool) -> Optional[str]:
        """
        Creates a container to run a crawl job but does not wait for it.
        Returns the job ID, or None on failure.
        """
        job_id = str(uuid.uuid4())
        logger.info(f"Job {job_id}: Received request for URL '{github_url}'.")
        
        command = [github_url, str(include_hidden)]
        container_id = self.docker_manager.create_and_run_container(self.IMAGE_NAME, command)

        if not container_id:
            logger.error(f"Job {job_id}: Failed to create Docker container.")
            return None

        logger.info(f"Job {job_id}: Container {container_id} created successfully.")
        self.jobs[job_id] = {
            "url": github_url,
            "status": "running", # Job is immediately considered running
            "container_id": container_id,
            "result": None,
            "error": None
        }
        return job_id

    def get_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """
        Checks a job's status by inspecting its container.
        If the container has finished, this method processes the results.
        """
        job = self.jobs.get(job_id)
        if not job:
            return None

        # If job is already processed, just return it to avoid re-processing
        if job["status"] in ["completed", "failed"]:
            return job

        container_id = job["container_id"]
        container_status = self.docker_manager.get_container_status(container_id)

        if container_status in ["running", "created"]:
            # The work is still in progress.
            job["status"] = "running"
        
        elif container_status == "exited":
            # The container has finished. Time to process the results.
            logger.info(f"Job {job_id}: Container {container_id} has exited. Processing results.")
            result = self.docker_manager.wait_for_container(container_id)
            logs = self.docker_manager.get_container_logs(container_id)

            if result.get("StatusCode") == 0:
                parsed_result = self._extract_result_from_logs(logs["stdout"])
                if parsed_result:
                    job["status"] = "completed"
                    job["result"] = parsed_result
                    logger.info(f"Job {job_id}: Successfully processed and marked as completed.")
                else:
                    job["status"] = "failed"
                    job["error"] = "Could not parse result from container logs."
                    logger.error(f"Job {job_id}: {job['error']}")
            else:
                job["status"] = "failed"
                job["error"] = logs["stderr"].strip() or "Container failed with a non-zero exit code."
                logger.error(f"Job {job_id}: Marked as failed. Error: {job['error']}")
        else:
            # Container is gone or in an unknown state
            job["status"] = "failed"
            job["error"] = f"Container {container_id} is lost or in an unrecoverable state."
            logger.error(f"Job {job_id}: {job['error']}")
            
        return job

    def close_crawl_job(self, job_id: str) -> bool:
        """
        Stops and removes the container associated with a job and deletes the job record.
        """
        job = self.jobs.get(job_id)
        if not job:
            logger.warning(f"Attempted to close non-existent job {job_id}")
            return False
        
        container_id = job.get("container_id")
        if container_id:
            logger.info(f"Job {job_id}: Cleaning up container {container_id}.")
            self.docker_manager.cleanup_container(container_id)
        
        # Remove job from memory
        del self.jobs[job_id]
        logger.info(f"Job {job_id} has been closed and removed.")
        return True

    def _extract_result_from_logs(self, logs: str) -> Optional[Dict[str, Any]]:
        """Extracts the JSON result from container stdout logs."""
        try:
            start_marker = "GITCRAWL_RESULT_START"
            end_marker = "GITCRAWL_RESULT_END"
            start_idx = logs.find(start_marker)
            end_idx = logs.find(end_marker)
            
            if start_idx != -1 and end_idx != -1:
                json_str = logs[start_idx + len(start_marker):end_idx].strip()
                return json.loads(json_str)
            return None
        except Exception:
            logger.error("Failed to parse JSON from logs.")
            return None