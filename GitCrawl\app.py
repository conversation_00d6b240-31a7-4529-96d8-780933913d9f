# app.py

from flask import Flask, request, jsonify
import logging
import sys
import os

from docker_manager import DockerManager
from orchestration_service import OrchestrationService
from flask_cors import CORS

# --- Enhanced Logging Configuration ---
logger = logging.getLogger("GitCrawlApp")
logger.setLevel(logging.INFO)
handler = logging.StreamHandler(sys.stdout)
formatter = logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
handler.setFormatter(formatter)
if not logger.handlers:
    logger.addHandler(handler)
# --- End of Logging Configuration ---

app = Flask(__name__)
CORS(app)

# Initialize services
try:
    docker_manager = DockerManager()
    orchestration_service = OrchestrationService(docker_manager)
    docker_available = True
    logger.info("Orchestration service with Docker support initialized successfully.")
except Exception as e:
    orchestration_service = None
    docker_available = False
    logger.warning(f"Docker not available, crawl service will be disabled. Reason: {e}")

@app.route('/', methods=['GET'])
def health_check():
    """Health check endpoint."""
    return jsonify({
        "service": "GitCrawlOrchestrator",
        "status": "healthy",
        "version": "3.0.0",
        "docker_available": docker_available
    })

@app.route('/crawl', methods=['POST'])
def create_crawl_job():
    """
    Creates a container and a job to crawl a repository.
    The container is left running.
    """
    if not orchestration_service:
        return jsonify({"error": "Service is unavailable, Docker not connected"}), 503

    if not request.is_json:
        return jsonify({"error": "Request must be JSON"}), 400
    
    data = request.get_json()
    github_url = data.get('url')
    if not github_url:
        return jsonify({"error": "Missing required field: url"}), 400

    logger.info(f"Received job creation request for URL: {github_url}")
    job_id = orchestration_service.start_crawl_job(github_url, data.get('include_hidden', False))
    
    if not job_id:
        return jsonify({"error": "Failed to create crawl job, check Docker service."}), 503

    status_url = f"/crawl/status/{job_id}"
    
    response = jsonify({
        "message": "Crawl job and container created successfully.",
        "job_id": job_id,
        "status_url": status_url
    })
    response.status_code = 201 # 201 Created is more appropriate now
    response.headers['Location'] = status_url
    return response

@app.route('/crawl/status/<job_id>', methods=['GET'])
def get_crawl_status(job_id: str):
    """
    Checks the status of a crawl job. If the job has finished,
    this call will process and store the final result.
    """
    if not orchestration_service:
        return jsonify({"error": "Service is unavailable, Docker not connected"}), 503

    job = orchestration_service.get_job_status(job_id)

    if not job:
        return jsonify({"error": "Job not found"}), 404
    
    return jsonify(job), 200

@app.route('/crawl/jobs/<job_id>', methods=['DELETE'])
def close_crawl_job(job_id: str):
    """
    Stops the container and deletes the crawl job record.
    """
    if not orchestration_service:
        return jsonify({"error": "Service is unavailable, Docker not connected"}), 503

    success = orchestration_service.close_crawl_job(job_id)
    
    if not success:
        return jsonify({"error": "Job not found"}), 404
    
    return jsonify({"message": f"Job {job_id} and associated container have been closed."}), 200

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 8000))
    debug = os.environ.get('FLASK_ENV') == 'development'
    
    if debug:
        logger.setLevel(logging.DEBUG)
        logger.info("Flask is running in DEBUG mode.")
        
    logger.info(f"Starting GitCrawl orchestrator service on http://0.0.0.0:{port}")
    app.run(host='0.0.0.0', port=port, debug=debug)