import os
import sys
import json
import shutil
from git import Repo

def build_tree(path, include_hidden):
    """Recursively builds a dictionary representing the directory tree."""
    items = []
    try:
        for item_name in sorted(os.listdir(path)):
            if not include_hidden and item_name.startswith("."):
                continue
            
            item_path = os.path.join(path, item_name)
            
            if os.path.isdir(item_path):
                items.append({
                    "name": item_name,
                    "type": "directory",
                    "children": build_tree(item_path, include_hidden)
                })
            else:
                items.append({"name": item_name, "type": "file"})
    except Exception as e:
        # Log permission errors etc. to stderr
        print(f"Warning: Could not read path {path}. Error: {e}", file=sys.stderr)
    return items

def main():
    # Arguments are passed from the DockerManager
    if len(sys.argv) != 3:
        print("Usage: python container_crawler.py <repo_url> <include_hidden>", file=sys.stderr)
        sys.exit(1)

    repo_url = sys.argv[1]
    include_hidden = sys.argv[2].lower() == 'true'
    clone_dir = "/app/repo" # A predictable directory inside the container

    try:
        print(f"Cloning repository: {repo_url}", file=sys.stderr)
        Repo.clone_from(repo_url, clone_dir, depth=1)
        print("Clone complete. Building tree...", file=sys.stderr)

        tree = build_tree(clone_dir, include_hidden)
        
        repo_name = repo_url.split("/")[-1].replace(".git", "")
        result = {
            "repository": {"url": repo_url, "name": repo_name},
            "tree": tree
        }
        
        # Print the final JSON result to stdout, wrapped in markers
        print("GITCRAWL_RESULT_START")
        print(json.dumps(result, indent=2))
        print("GITCRAWL_RESULT_END")

    except Exception as e:
        print(f"An error occurred: {str(e)}", file=sys.stderr)
        sys.exit(1)
    finally:
        # Clean up the cloned repo
        if os.path.exists(clone_dir):
            shutil.rmtree(clone_dir, ignore_errors=True)

if __name__ == "__main__":
    main()