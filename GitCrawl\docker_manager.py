# docker_manager.py

import docker
import logging
from typing import Dict, Any, Optional, List

logger = logging.getLogger(__name__)

class DockerManager:
    """Manages the lifecycle of Docker containers."""

    def __init__(self):
        try:
            logger.info("Initializing DockerManager...")
            self.client = docker.from_env()
            self.client.ping()
            logger.info("Docker daemon connection successful.")
        except Exception as e:
            logger.critical(f"Failed to connect to Docker daemon: {e}")
            raise

    def create_and_run_container(self, image: str, command: List[str]) -> Optional[str]:
        """
        Creates and runs a container in detached mode.

        Args:
            image: The name of the Docker image to use.
            command: The command to run inside the container.

        Returns:
            The ID of the created container, or None if it fails.
        """
        logger.info(f"Creating container from image '{image}' with command: {' '.join(command)}")
        try:
            container = self.client.containers.run(
                image=image,
                command=command,
                detach=True,  # Run in the background
                remove=False  # Keep the container after it exits to inspect logs
            )
            return container.id
        except docker.errors.ImageNotFound:
            logger.error(f"Docker image '{image}' not found.")
            return None
        except Exception as e:
            logger.error(f"Failed to create and run container: {e}")
            return None

    def get_container_status(self, container_id: str) -> Optional[str]:
        """
        Gets the real-time status of a container (e.g., 'created', 'running', 'exited').

        Returns:
            The status string or None if the container is not found.
        """
        try:
            container = self.client.containers.get(container_id)
            return container.status
        except docker.errors.NotFound:
            logger.warning(f"Attempted to get status of non-existent container {container_id}")
            return None
        except Exception as e:
            logger.error(f"Error getting status for container {container_id}: {e}")
            return None

    def wait_for_container(self, container_id: str) -> Dict[str, Any]:
        """
        Waits for a container to finish and returns its exit code.
        Should only be called on a container that is known to have exited.

        Returns:
            A dictionary containing the status code. e.g., {'StatusCode': 0}
        """
        try:
            container = self.client.containers.get(container_id)
            result = container.wait()
            logger.info(f"Container {container_id} finished with status code {result.get('StatusCode')}")
            return result
        except docker.errors.NotFound:
            logger.error(f"Container {container_id} not found while waiting.")
            return {"StatusCode": -1, "Error": "Container not found"}
        except Exception as e:
            logger.error(f"Error waiting for container {container_id}: {e}")
            return {"StatusCode": -1, "Error": str(e)}

    def get_container_logs(self, container_id: str) -> Dict[str, str]:
        """
        Retrieves stdout and stderr logs from a finished container.
        """
        try:
            container = self.client.containers.get(container_id)
            return {
                "stdout": container.logs(stdout=True, stderr=False).decode('utf-8'),
                "stderr": container.logs(stdout=False, stderr=True).decode('utf-8')
            }
        except Exception as e:
            logger.error(f"Failed to get logs for container {container_id}: {e}")
            return {"stdout": "", "stderr": f"Failed to retrieve logs: {e}"}

    def cleanup_container(self, container_id: str):
        """
        Stops (if running) and removes a container.
        """
        try:
            container = self.client.containers.get(container_id)
            container.remove(force=True)
            logger.info(f"Successfully cleaned up container {container_id}.")
        except docker.errors.NotFound:
            # Container was already removed, which is fine.
            logger.info(f"Container {container_id} was already removed.")
            pass
        except Exception as e:
            logger.warning(f"Could not clean up container {container_id}: {e}")